<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="formRules"
    v-loading="formLoading"
    label-width="0px"
    :inline-message="true"
  >
    <el-table :data="formData" class="-mt-10px" show-summary :summary-method="getSummaries">
      <el-table-column label="序号" type="index" width="60" align="center"/>
      <!-- 核心业务信息 - 固定在左侧 -->
      <el-table-column label="物料信息" min-width="280">
        <template #default="{ row }">
          <div class="material-info-cell">
            <!-- 物料名称 -->
            <div class="info-row">
              <span class="info-label">物料:</span>
              <span class="info-value">{{ formatMaterialDisplay(row) }}</span>
            </div>
            <!-- 批号 -->
            <div class="info-row">
              <span class="info-label">批号:</span>
              <span class="info-value">{{ row.batchNo || '-' }}</span>
            </div>
            <!-- 来源信息 -->
            <div class="info-row">
              <span class="info-label">来源:</span>
              <span class="info-value">{{ getSourceInfoDisplay(row) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商名称" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.supplierName`" :rules="formRules.supplierName" class="mb-0px!">
            <ScrollSelect
              v-model="row.supplierId"
              :loadMethod="loadSuppliers"
              :labelKey="'name'"
              :valueKey="'id'"
              :queryKey="'name'"
              :defaultValue="row.supplierDefaultValue"
              placeholder="请选择供应商"
              disabled
              @change="(value, item) => handleSupplierChange($index, value, item)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="数量" min-width="180">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.quantity`" :rules="formRules.quantity" class="mb-0px!">
            <el-input-number
              v-model="row.quantity"
              placeholder="请输入数量"
              :precision="4"
              :step="0.0001"
              :min="0"
              controls-position="right"
              class="w-full"
              @change="() => handleQuantityChange($index)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单位" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unit`" :rules="formRules.unit" class="mb-0px!">
            <el-select
              v-model="row.unit"
              placeholder="请选择单位"
              clearable
              filterable
              @change="(value) => handleMainUnitChange($index, value)"
              disabled
            >
              <el-option
                v-for="unit in unitList"
                :key="unit.id"
                :label="unit.name"
                :value="unit.id"
              />
            </el-select>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="单价" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitPrice`" :rules="formRules.unitPrice" class="mb-0px!">
            <el-input
              v-model="row.unitPrice"
              placeholder="请输入单价"
              @input="() => handleUnitPriceChange($index)"
            />
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="税率%" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.tax`" :rules="formRules.tax" class="mb-0px!">
            <el-input
              v-model="row.tax"
              placeholder="请输入税率"
              @input="() => handleTaxRateChange($index)"
            />
          </el-form-item>
        </template>
      </el-table-column>

      <!-- 价格计算信息 -->
      <el-table-column label="金额" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.amount`" :rules="formRules.amount" class="mb-0px!">
            <el-input v-model="row.amount" placeholder="请输入金额"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="含税单价" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.unitTaxPrice`" :rules="formRules.unitTaxPrice" class="mb-0px!">
            <el-input v-model="row.unitTaxPrice" placeholder="请输入含税单价"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="税额" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.taxAmount`" :rules="formRules.taxAmount" class="mb-0px!">
            <el-input v-model="row.taxAmount" placeholder="请输入税额"/>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="价税合计" min-width="140">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.totalAmount`" :rules="formRules.totalAmount" class="mb-0px!">
            <el-input v-model="row.totalAmount" placeholder="请输入价税合计"/>
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column label="批号" min-width="120">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.batchNo`" :rules="formRules.batchNo" class="mb-0px!">
            <el-input v-model="row.batchNo" placeholder="请输入批号" />
          </el-form-item>
        </template>
      </el-table-column>
      <!-- 采购信息 -->
      <el-table-column label="备注" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.remark`" :rules="formRules.remark" class="mb-0px!">
            <el-input v-model="row.remark" placeholder="请输入备注" />
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column label="合同单号" min-width="150">
        <template #default="{ row, $index }">
          <el-form-item :prop="`${$index}.contractNo`" :rules="formRules.contractNo" class="mb-0px!">
            <el-input v-model="row.contractNo" placeholder="请输入合同单号"/>
          </el-form-item>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed="right" label="操作" width="60">
        <template #default="{ $index }">
          <Icon icon="ep:delete" @click="handleDelete($index)" color="#f56c6c"/>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
  <el-row justify="center" class="mt-3">
    <el-button @click="handleAdd" round>+ 添加采购订单产品</el-button>
  </el-row>
</template>
<script setup lang="ts">
import { onMounted, nextTick } from 'vue'
import { OrderApi } from '@/api/scm/purchase/order'
import { CompanyApi } from '@/api/scm/base/company'
import { MaterialApi } from '@/api/scm/base/material'
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { getRemoteUnitMap } from '@/utils/commonBiz'

import { formatAmount, formatQuantity } from '@/utils/formatter'

const props = defineProps<{
  orderId?: number // 采购订单ID（主表的关联字段）
  supplierId?: number // 供应商ID
  supplierName?: string // 供应商名称
  orderDate?: string | number // 订单日期
  orderNo?: string // 订单编号
}>()

// 定义事件
const emit = defineEmits<{
  totalAmountChange: [amount: number]
}>()

// 组件挂载后检查并计算金额
onMounted(() => {
  // 延迟检查，以防数据是在挂载后才设置的
  setTimeout(() => {
    // 如果有数据但没有计算金额，手动触发计算
    if (formData.value.length > 0) {
      formData.value.forEach((_, index) => {
        calculateAmount(index)
      })
      // 计算总金额
      calculateOrderTotalAmount()
    }
  }, 1000)
})
const formLoading = ref(false) // 表单的加载中
const formData = ref<any[]>([])
const formRules = reactive<any>({
  orderId: [{ required: true, message: '采购订单ID不能为空', trigger: 'blur' }],
  quantity: [{ required: true, message: '物料数量不能为空', trigger: 'blur' }],
})
const formRef = ref<any>() // 表单 Ref

// 单位映射缓存
const unitMap = ref<Map<number, any>>(new Map())
// 单位列表数据
const unitList = ref<any[]>([])

/** 初始化单位映射 */
const initUnitMap = async () => {
  try {
    const units = await getRemoteUnitMap()
    // 将对象转换为Map和Array
    unitMap.value.clear()
    unitList.value = []
    
    Object.keys(units).forEach(key => {
      const unitId = parseInt(key)
      const unitInfo = units[key]
      unitMap.value.set(unitId, unitInfo)
      unitList.value.push(unitInfo)
    })
    

  } catch (error) {
    console.error('初始化单位映射失败:', error)
  }
}

/** 根据单位ID或名称获取单位信息 */
const getUnitInfo = (unitIdOrName: number | string) => {
  if (!unitIdOrName) return null
  
  // 首先尝试按ID查找
  const id = typeof unitIdOrName === 'string' ? parseInt(unitIdOrName) : unitIdOrName
  if (!isNaN(id)) {
    const unitById = unitMap.value.get(id)
    if (unitById) return unitById
  }
  
  // 如果按ID没有找到，或者传入的是非数字字符串，则按名称查找
  if (typeof unitIdOrName === 'string') {
    for (const [, unitInfo] of unitMap.value.entries()) {
      if (unitInfo.name === unitIdOrName) {
        return unitInfo
      }
    }
  }
  
  return null
}

/** 格式化物料显示信息 */
const formatMaterialDisplay = (row: any) => {
  if (!row) return '-'
  const code = row.materialCode || ''
  const name = row.materialName || ''

  // 构建显示标签，只有非空值才参与拼接
  const parts: string[] = []
  if (code) parts.push(code)
  if (name) parts.push(name)

  return parts.length > 0 ? parts.join(' - ') : '-'
}

/** 获取源单类型标签 */
const getSourceTypeLabel = (sourceType: string) => {
  if (!sourceType) return '-'
  const dictOptions = getStrDictOptions(DICT_TYPE.PURCHASE_REQ_SOURCE)
  const option = dictOptions.find(item => item.value === sourceType)
  return option ? option.label : sourceType
}

/** 获取来源信息显示（来源类型-来源单号格式） */
const getSourceInfoDisplay = (row: any) => {
  const sourceTypeLabel = getSourceTypeLabel(row.sourceType)
  let sourceNo = ''

  // 如果有多个需求单，显示合并的来源单号
  if (row.records && Array.isArray(row.records) && row.records.length > 1) {
    sourceNo = getMultipleSourceNos(row)
  } else {
    // 否则显示单个来源单号
    sourceNo = row.sourceNo || ''
  }

  // 如果都没有值，返回 '-'
  if (!sourceTypeLabel || sourceTypeLabel === '-') {
    return sourceNo || '-'
  }

  if (!sourceNo || sourceNo === '-') {
    return sourceTypeLabel
  }

  // 格式：来源类型-来源单号
  return `${sourceTypeLabel} - ${sourceNo}`
}

/** 获取多个来源单号的显示文本 */
const getMultipleSourceNos = (row: any) => {
  if (!row.records || !Array.isArray(row.records) || row.records.length === 0) {
    return row.sourceNo || ''
  }

  // 提取所有需求单号，去重并过滤空值
  const requirementNos = row.records
    .map((record: any) => record.requirementNo)
    .filter((no: string) => no && no.trim())
    .filter((no: string, index: number, arr: string[]) => arr.indexOf(no) === index) // 去重

  if (requirementNos.length === 0) {
    return row.sourceNo || ''
  }

  if (requirementNos.length === 1) {
    return requirementNos[0]
  }

  // 如果有多个，显示第一个加上数量提示
  return `${requirementNos[0]} 等${requirementNos.length}个`
}

/** 监听主表的关联字段的变化，加载对应的子表数据 */
watch(
  () => props.orderId,
  async (val) => {
    // 1. 重置表单
    formData.value = []
    // 2. val 非空，则加载数据
    if (!val) {
      return;
    }
    try {
      formLoading.value = true

      // 3. 确保单位映射已初始化
      if (unitMap.value.size === 0) {
        await initUnitMap()
      }

      const data: any = await OrderApi.getOrderDetailListByOrderId(val)

      // 4. 为每个行项目设置默认值，以便 ScrollSelect 能正确显示名称
      for (const row of data) {
        await setRowDefaultValues(row)
      }

      formData.value = data

      // 5. 数据加载完成后，为每一行重新计算金额（确保金额正确）
      formData.value.forEach((_, index) => {
        calculateAmount(index)
      })

      // 6. 计算并通知父组件总金额
      calculateOrderTotalAmount()
    } finally {
      formLoading.value = false
    }
  },
  { immediate: true }
)

/** 监听主表供应商变化，同步更新所有子表行的供应商信息 */
watch(
  () => [props.supplierId, props.supplierName],
  ([newSupplierId, newSupplierName]) => {
    // 更新所有现有行的供应商信息
    formData.value.forEach(row => {
      row.supplierId = newSupplierId
      row.supplierName = newSupplierName
      row.supplierDefaultValue = newSupplierId && newSupplierName ? {
        id: newSupplierId,
        name: newSupplierName
      } : undefined
    })
  },
  { immediate: false }
)

/** 监听formData变化，自动计算金额 */
watch(
  () => formData.value,
  (newData) => {
    if (newData && newData.length > 0) {
      // 延迟一点时间，确保数据完全设置完成
      nextTick(() => {
        let hasCalculated = false
        newData.forEach((row, index) => {
          const quantity = Number(row.quantity) || 0
          const unitPrice = Number(row.unitPrice) || 0
          if (quantity > 0 && unitPrice > 0 && (!row.amount || row.amount === 0)) {
            calculateAmount(index)
            hasCalculated = true
          }
        })
        // 如果有计算，则重新计算总金额
        if (hasCalculated) {
          calculateOrderTotalAmount()
        }
      })
    }
  },
  { deep: true, immediate: true }
)

/** 新增按钮操作 */
const handleAdd = () => {
  const row: any = {
    id: undefined,
    orderId: undefined,
    orderNo: undefined,
    orderDate: undefined,
    supplierId: undefined,
    supplierName: undefined,
    supplierDefaultValue: undefined,
    materialId: undefined,
    materialName: undefined,
    materialDefaultValue: undefined,
    quantity: undefined,
    unit: undefined,
    unitId: undefined,
    unitDefaultValue: undefined,
    unitPrice: undefined,
    unitTaxPrice: undefined,
    amount: undefined,
    tax: undefined,
    taxAmount: undefined,
    totalAmount: undefined,
    receivedQuantity: undefined,
    receivedDate: undefined,
    batchNo: undefined,
    requirementId: undefined,
    sourceType: undefined,
    sourceNo: undefined,
    remark: undefined,
    progressStatus: undefined,
    follower: undefined,
    followDate: undefined,
    progressRemark: undefined,
    inStockQuantity: undefined,
    auxiliaryUnit: undefined,
    auxiliaryUnitId: undefined,
    auxiliaryUnitDefaultValue: undefined,
    auxiliaryQuantity: undefined,
    inAuxiliaryQuantity: undefined,
    inTransitQuantity: undefined,
    relateQuantity: undefined,
    contractNo: undefined,
    invoiceQuantity: undefined,
    invoiceAuxiliaryQuantity: undefined,
  }

  // 从父组件获取数据并自动填充
  row.orderId = props.orderId
  row.orderNo = props.orderNo
  row.orderDate = props.orderDate

  // 设置供应商信息
  if (props.supplierId && props.supplierName) {
    row.supplierId = props.supplierId
    row.supplierName = props.supplierName
    row.supplierDefaultValue = {
      id: props.supplierId,
      name: props.supplierName
    }
  }

  formData.value.push(row)
}

/** 删除按钮操作 */
const handleDelete = (index: number) => {
  formData.value.splice(index, 1)
  // 删除后重新计算总金额
  calculateOrderTotalAmount()
}



/** 为行项目设置默认值，用于 ScrollSelect 显示名称 */
const setRowDefaultValues = async (row: any) => {
  // 设置供应商默认值
  if (row.supplierId && row.supplierName) {
    row.supplierDefaultValue = {
      id: row.supplierId,
      name: row.supplierName
    }
  }

  // 设置物料默认值并获取完整物料信息
  if (row.materialId && row.materialName) {
    row.materialDefaultValue = {
      id: row.materialId,
      name: row.materialName
    }
    // 如果物料信息不完整（缺少价格信息），从接口获取完整信息
    if (row.materialId && (row.unitPrice === 0 || !row.unitPrice)) {
      try {
        const materialDetail = await MaterialApi.getMaterial(row.materialId)
        // 设置采购价格
        if (materialDetail.purchasePrice !== undefined && materialDetail.purchasePrice !== null) {
          row.unitPrice = Number(materialDetail.purchasePrice)

          // 获取税率
          const taxRate = Number(row.tax) || 0

          // 计算含税单价 - 四舍五入保留两位小数
          row.unitTaxPrice = formatAmountValue(Number(materialDetail.purchasePrice) * (1 + taxRate / 100))

          // 如果有数量，自动计算金额
          const quantity = Number(row.quantity) || 0
          if (quantity > 0) {
            // 计算基础金额 - 四舍五入保留两位小数
            const amount = formatAmountValue(quantity * Number(materialDetail.purchasePrice))
            row.amount = amount

            // 计算税额和价税合计 - 四舍五入保留两位小数
            const taxAmount = formatAmountValue(amount * (taxRate / 100))
            row.taxAmount = taxAmount
            row.totalAmount = formatAmountValue(amount + taxAmount)
          }
        }
      } catch (error) {
        console.error('获取物料详细信息失败:', error)
      }
    }
  }

  // 确保金额字段正确计算（即使已有单价也要重新计算）
  const quantity = Number(row.quantity) || 0
  const unitPrice = Number(row.unitPrice) || 0
  const taxRate = Number(row.tax) || 0

  if (quantity > 0 && unitPrice > 0) {
    // 计算基础金额 - 四舍五入保留两位小数
    const amount = formatAmountValue(quantity * unitPrice)
    row.amount = amount

    // 计算含税单价 - 四舍五入保留两位小数
    row.unitTaxPrice = formatAmountValue(unitPrice * (1 + taxRate / 100))

    // 计算税额和价税合计 - 四舍五入保留两位小数
    const taxAmount = formatAmountValue(amount * (taxRate / 100))
    row.taxAmount = taxAmount
    row.totalAmount = formatAmountValue(amount + taxAmount)
  }

  // 设置单位默认值
  if (row.unit) {
    const unitInfo = getUnitInfo(row.unit)
    if (unitInfo) {
      row.unitId = unitInfo.id
      row.unitDefaultValue = {
        id: unitInfo.id,
        name: unitInfo.name
      }
      // row.unit 保持为ID值，用于v-model绑定
      row.unit = unitInfo.id
    } else {
      // 如果在缓存中找不到，保持原值
      row.unitId = row.unit
    }
  }

  // 设置辅助单位默认值
  if (row.auxiliaryUnit) {
    const auxiliaryUnitInfo = getUnitInfo(row.auxiliaryUnit)
    if (auxiliaryUnitInfo) {
      row.auxiliaryUnitId = auxiliaryUnitInfo.id
      row.auxiliaryUnitDefaultValue = {
        id: auxiliaryUnitInfo.id,
        name: auxiliaryUnitInfo.name
      }
      // 更新 row.auxiliaryUnit 为单位名称（用于显示）
      row.auxiliaryUnit = auxiliaryUnitInfo.name
    } else {
      // 如果在缓存中找不到，保持原值
      row.auxiliaryUnitId = row.auxiliaryUnit
    }
  }
}

/** 加载供应商数据 */
const loadSuppliers = async (params: any) => {
  try {
    const response: any = await CompanyApi.getSimpleCompanyPage({
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 20,
      name: params.name || params.query || '',
      isSupplier: true // 只获取供应商
    })
    return {
      list: response.list || [],
      total: response.total || 0
    }
  } catch (error) {
    console.error('加载供应商失败:', error)
    return { list: [], total: 0 }
  }
}


/** 供应商变更处理 */
const handleSupplierChange = (index: number, _selectedValue: any, selectedItem: any) => {
  if (selectedItem) {
    formData.value[index].supplierId = selectedItem.id
    formData.value[index].supplierName = selectedItem.name || selectedItem.shortName
    // 设置默认值，用于下次显示
    formData.value[index].supplierDefaultValue = {
      id: selectedItem.id,
      name: selectedItem.name || selectedItem.shortName
    }
  } else {
    formData.value[index].supplierId = undefined
    formData.value[index].supplierName = undefined
    formData.value[index].supplierDefaultValue = undefined
  }
}
/** 通用单位变更处理 */
const handleUnitChange = (index: number, unitType: 'unit' | 'auxiliaryUnit', selectedValue: any) => {
  if (selectedValue) {
    // 根据选中的ID查找单位信息
    const unitInfo = unitMap.value.get(selectedValue)
    
    if (unitType === 'unit') {
      formData.value[index].unitId = selectedValue
      formData.value[index].unit = selectedValue // 存储ID值，用于v-model绑定
      // 设置默认值，用于显示名称
      if (unitInfo) {
        formData.value[index].unitDefaultValue = {
          id: unitInfo.id,
          name: unitInfo.name
        }
      }
    } else {
      formData.value[index].auxiliaryUnitId = selectedValue // v-model绑定的字段
      formData.value[index].auxiliaryUnit = unitInfo?.name || '' // 存储名称，用于其他用途
      // 设置默认值，用于显示名称
      if (unitInfo) {
        formData.value[index].auxiliaryUnitDefaultValue = {
          id: unitInfo.id,
          name: unitInfo.name
        }
      }
    }
  } else {
    if (unitType === 'unit') {
      formData.value[index].unitId = undefined
      formData.value[index].unit = undefined
      formData.value[index].unitDefaultValue = undefined
    } else {
      formData.value[index].auxiliaryUnitId = undefined
      formData.value[index].auxiliaryUnit = undefined
      formData.value[index].auxiliaryUnitDefaultValue = undefined
    }
  }
}

/** 主单位变更处理 */
const handleMainUnitChange = (index: number, selectedValue: any) => {
  handleUnitChange(index, 'unit', selectedValue)
}


/** 数量变化处理 - 自动计算金额 */
const handleQuantityChange = (index: number) => {
  // 格式化数量为四位小数
  const row = formData.value[index]
  if (row && row.quantity) {
    const quantity = Number(row.quantity)
    if (!isNaN(quantity)) {
      row.quantity = formatQuantityValue(quantity)
    }
  }
  calculateAmount(index)
}

/** 单价变化处理 - 自动计算金额 */
const handleUnitPriceChange = (index: number) => {
  // 格式化单价为两位小数
  const row = formData.value[index]
  if (row && row.unitPrice) {
    const unitPrice = Number(row.unitPrice)
    if (!isNaN(unitPrice)) {
      row.unitPrice = formatAmountValue(unitPrice)
    }
  }
  calculateAmount(index)
}

/** 税率变化处理 - 自动计算含税金额 */
const handleTaxRateChange = (index: number) => {
  // 格式化税率为两位小数
  const row = formData.value[index]
  if (row && row.tax) {
    const tax = Number(row.tax)
    if (!isNaN(tax)) {
      row.tax = formatAmountValue(tax)
    }
  }
  calculateAmount(index)
}

/** 格式化金额 - 四舍五入保留两位小数 */
const formatAmountValue = (value: number): number => {
  return Math.round(value * 100) / 100
}

/** 格式化数量 - 保留四位小数 */
const formatQuantityValue = (value: number): number => {
  return Math.round(value * 10000) / 10000
}

/** 计算金额相关字段 */
const calculateAmount = (index: number) => {
  const row = formData.value[index]
  if (!row) return

  const quantity = Number(row.quantity) || 0
  const unitPrice = Number(row.unitPrice) || 0
  const taxRate = Number(row.tax) || 0

  // 计算基础金额（不含税）- 四舍五入保留两位小数
  const amount = formatAmountValue(quantity * unitPrice)
  row.amount = amount

  // 计算含税单价 - 四舍五入保留两位小数
  const unitTaxPrice = formatAmountValue(unitPrice * (1 + taxRate / 100))
  row.unitTaxPrice = unitTaxPrice

  // 计算税额 - 四舍五入保留两位小数
  const taxAmount = formatAmountValue(amount * (taxRate / 100))
  row.taxAmount = taxAmount

  // 计算价税合计 - 四舍五入保留两位小数
  const totalAmount = formatAmountValue(amount + taxAmount)
  row.totalAmount = totalAmount

  // 计算完单行金额后，重新计算订单总金额
  calculateOrderTotalAmount()
}

/** 计算订单总金额并通知父组件 */
const calculateOrderTotalAmount = () => {
  // 计算所有行的价税合计总和
  const orderTotalAmount = formData.value.reduce((total, row) => {
    const rowTotalAmount = Number(row.totalAmount) || 0
    return total + rowTotalAmount
  }, 0)

  // 格式化总金额
  const formattedTotal = formatAmountValue(orderTotalAmount)

  // 通知父组件更新总金额
  emit('totalAmountChange', formattedTotal)
}

/** 表格汇总方法 */
const getSummaries = (param: any) => {
  const { columns, data } = param
  const sums: string[] = []


  // 定义列索引对应的字段映射（根据表格列的顺序）
  const columnFieldMap: { [key: number]: string } = {
    0: '', // 序号列
    1: '', // 物料信息
    2: '', // 供应商名称
    3: 'quantity', // 数量
    4: '', // 单位
    5: 'unitPrice', // 单价
    6: '', // 税率%
    7: 'amount', // 金额
    8: 'unitTaxPrice', // 含税单价
    9: 'taxAmount', // 税额
    10: 'totalAmount', // 价税合计
    11: '', // 批号
    12: '', // 备注
    13: '', // 合同单号
    14: '' // 操作
  }

  columns.forEach((_column: any, index: number) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    const fieldName = columnFieldMap[index]
    if (!fieldName) {
      sums[index] = ''
      return
    }

    // 需要汇总的数量字段
    const quantityFields = [
      'quantity'
    ]

    // 需要汇总的金额字段
    const amountFields = [
      'unitPrice', 'amount', 'unitTaxPrice',
      'totalAmount', 'taxAmount'
    ]

    if (quantityFields.includes(fieldName)) {
      // 数量字段汇总
      const values = data.map((item: any) => Number(item[fieldName]) || 0)
      const sum = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = formatQuantity(sum)
    } else if (amountFields.includes(fieldName)) {
      // 金额字段汇总
      const values = data.map((item: any) => Number(item[fieldName]) || 0)
      const sum = values.reduce((prev: number, curr: number) => prev + curr, 0)
      sums[index] = formatAmount(sum)
    } else {
      sums[index] = ''
    }
  })

  return sums
}

/** 表单校验 */
const validate = () => {
  return formRef.value.validate()
}

/** 表单值 */
const getData = () => {
  return formData.value
}

/** 设置需求数据，用于从采购需求转换为采购订单 */
const setRequirementData = async (requirementData: any) => {
  if (!requirementData) return
  
  // 确保单位映射已初始化
  if (unitMap.value.size === 0) {
    await initUnitMap()
  }
  
  // 创建新的订单详情行
  const newRow: any = {
    id: undefined,
    orderId: props.orderId,
    orderNo: props.orderNo,
    orderDate: props.orderDate,
    supplierId: props.supplierId,
    supplierName: props.supplierName,
    supplierDefaultValue: props.supplierId && props.supplierName ? {
      id: props.supplierId,
      name: props.supplierName
    } : undefined,
    materialId: requirementData.materialId,
    materialName: requirementData.materialName,
    materialCode: requirementData.materialCode,
    materialDefaultValue: requirementData.materialId && requirementData.materialName ? {
      id: requirementData.materialId,
      name: requirementData.materialName
    } : undefined,
    quantity: requirementData.quantity,
    unit: requirementData.unit,
    unitPrice: requirementData.unitPrice || 0,
    unitTaxPrice: 0,
    amount: requirementData.amount || 0,
    tax: 0,
    taxAmount: 0,
    totalAmount: requirementData.amount || 0,
    // 重要：填充来源信息
    requirementId: requirementData.id, // source_id 字段
    sourceType: requirementData.sourceType, // 采购需求单中的来源类型
    sourceNo: requirementData.sourceNo, // 采购需求单中的来源单号
    remark: requirementData.remark || '',
    progressStatus: undefined,
    follower: undefined,
    followDate: undefined,
    progressRemark: undefined,
    inStockQuantity: 0,
    auxiliaryUnit: undefined,
    auxiliaryUnitId: undefined,
    auxiliaryUnitDefaultValue: undefined,
    auxiliaryQuantity: 0,
    inAuxiliaryQuantity: 0,
    inTransitQuantity: 0,
    relateQuantity: 0,
    contractNo: undefined,
    invoiceQuantity: 0,
    invoiceAuxiliaryQuantity: 0,
  }

  // 处理单位信息
  if (requirementData.unit) {
    // 如果 requirementData.unit 是单位名称，需要查找对应的ID
    // 如果 requirementData.unit 是单位ID，需要获取单位信息
    const unitInfo = getUnitInfo(requirementData.unit)
    if (unitInfo) {
      newRow.unitId = unitInfo.id
      newRow.unitDefaultValue = {
        id: unitInfo.id,
        name: unitInfo.name
      }
      newRow.unit = unitInfo.id // el-select v-model 绑定的值
    } else {
      // 如果无法从缓存中找到单位信息，尝试直接使用传入的值
      newRow.unit = requirementData.unit
      newRow.unitId = requirementData.unit
    }
  }

  // 获取完整的物料信息并填充金额字段
  if (newRow.materialId && (newRow.unitPrice === 0 || !newRow.unitPrice)) {
    try {
      const materialDetail = await MaterialApi.getMaterialSimpleInfo(newRow.materialId)

      // 设置采购价格
      if (materialDetail.purchasePrice !== undefined && materialDetail.purchasePrice !== null) {
        newRow.unitPrice = Number(materialDetail.purchasePrice)

        // 获取税率
        const taxRate = Number(newRow.tax) || 0

        // 计算含税单价 - 四舍五入保留两位小数
        newRow.unitTaxPrice = formatAmountValue(Number(materialDetail.purchasePrice) * (1 + taxRate / 100))

        // 重新计算金额
        const quantity = Number(newRow.quantity) || 0
        if (quantity > 0) {
          // 计算基础金额 - 四舍五入保留两位小数
          const amount = formatAmountValue(quantity * Number(materialDetail.purchasePrice))
          newRow.amount = amount

          // 计算税额和价税合计 - 四舍五入保留两位小数
          const taxAmount = formatAmountValue(amount * (taxRate / 100))
          newRow.taxAmount = taxAmount
          newRow.totalAmount = formatAmountValue(amount + taxAmount)
        }
      }
    } catch (error) {
      console.error('获取物料详细信息失败:', error)
    }
  }

  // 添加到表单数据中
  formData.value.push(newRow)
}

/** 设置多个需求数据，用于批量转换采购需求为采购订单 */
const setMultipleRequirementData = async (requirementDataList: any[]) => {
  if (!requirementDataList || !Array.isArray(requirementDataList) || requirementDataList.length === 0) {
    return
  }

  // 确保单位映射已初始化
  if (unitMap.value.size === 0) {
    await initUnitMap()
  }

  // 清空现有数据
  formData.value = []

  // 合并相同物料的需求单
  const mergedMaterials = mergeSameMaterials(requirementDataList)

  // 为每个合并后的物料创建订单详情行
  for (const materialData of mergedMaterials) {
    const newRow: any = {
      id: undefined,
      orderId: props.orderId,
      orderNo: props.orderNo,
      orderDate: props.orderDate,
      supplierId: props.supplierId,
      supplierName: props.supplierName,
      supplierDefaultValue: props.supplierId && props.supplierName ? {
        id: props.supplierId,
        name: props.supplierName
      } : undefined,
      materialId: materialData.materialId,
      materialName: materialData.materialName,
      materialCode: materialData.materialCode,
      materialDefaultValue: materialData.materialId && materialData.materialName ? {
        id: materialData.materialId,
        name: materialData.materialName
      } : undefined,
      quantity: materialData.totalQuantity, // 使用合并后的总数量
      unit: materialData.unit,
      unitPrice: materialData.unitPrice || 0,
      unitTaxPrice: 0,
      amount: materialData.totalAmount || 0, // 使用合并后的总金额
      tax: 0,
      taxAmount: 0,
      totalAmount: materialData.totalAmount || 0,
      // 重要：填充来源信息 - 使用第一个需求单的信息
      requirementId: materialData.requirements[0].id, // source_id 字段
      sourceType: materialData.requirements[0].sourceType, // 采购需求单中的来源类型
      sourceNo: materialData.combinedSourceNo, // 合并后的来源单号
      remark: materialData.combinedRemark, // 合并后的备注
      progressStatus: undefined,
      follower: undefined,
      followDate: undefined,
      progressRemark: undefined,
      inStockQuantity: 0,
      auxiliaryUnit: undefined,
      auxiliaryUnitId: undefined,
      auxiliaryUnitDefaultValue: undefined,
      auxiliaryQuantity: 0,
      inAuxiliaryQuantity: 0,
      inTransitQuantity: 0,
      relateQuantity: 0,
      contractNo: undefined,
      invoiceQuantity: 0,
      invoiceAuxiliaryQuantity: 0,
      // 重要：添加 records 字段，存储多个需求单信息
      records: materialData.requirements.map((req: any) => ({
        id: undefined, // 新增记录，ID为空
        requirementId: req.id,
        requirementNo: req.requestNo,
        orderId: undefined, // 订单ID在保存时会被设置
        orderNo: undefined, // 订单编号在保存时会被设置
        purchaseId: undefined, // 采购ID在保存时会被设置
        purchaseNo: undefined, // 采购编号在保存时会被设置
        quantity: req.quantity, // 该需求单的数量
        remark: req.remark || ''
      }))
    }

    // 获取完整的物料信息并填充金额字段
    if (materialData.materialId && (newRow.unitPrice === 0 || !newRow.unitPrice)) {
      try {
        const materialDetail = await MaterialApi.getMaterial(materialData.materialId)

        // 设置采购价格
        if (materialDetail.purchasePrice !== undefined && materialDetail.purchasePrice !== null) {
          newRow.unitPrice = Number(materialDetail.purchasePrice)

          // 获取税率
          const taxRate = Number(newRow.tax) || 0

          // 计算含税单价 - 四舍五入保留两位小数
          newRow.unitTaxPrice = formatAmountValue(Number(materialDetail.purchasePrice) * (1 + taxRate / 100))

          // 重新计算金额
          const quantity = Number(newRow.quantity) || 0
          if (quantity > 0) {
            // 计算基础金额 - 四舍五入保留两位小数
            const amount = formatAmountValue(quantity * Number(materialDetail.purchasePrice))
            newRow.amount = amount

            // 计算税额和价税合计 - 四舍五入保留两位小数
            const taxAmount = formatAmountValue(amount * (taxRate / 100))
            newRow.taxAmount = taxAmount
            newRow.totalAmount = formatAmountValue(amount + taxAmount)
          }
        }
      } catch (error) {
        console.error('获取物料详细信息失败:', error)
      }
    }

    // 处理单位信息
    if (materialData.unit) {
      const unitInfo = getUnitInfo(materialData.unit)
      if (unitInfo) {
        newRow.unitId = unitInfo.id
        newRow.unitDefaultValue = {
          id: unitInfo.id,
          name: unitInfo.name
        }
        newRow.unit = unitInfo.id // el-select v-model 绑定的值
      } else {
        // 如果无法从缓存中找到单位信息，尝试直接使用传入的值
        newRow.unit = materialData.unit
        newRow.unitId = materialData.unit
      }
    }

    // 添加到表单数据中
    formData.value.push(newRow)
  }
}

/** 合并相同物料的需求单 */
const mergeSameMaterials = (requirementDataList: any[]) => {
  const materialMap = new Map()

  console.log('requirementDataList:', requirementDataList)
  for (const requirement of requirementDataList) {
    // 使用物料ID + 单位作为合并的键，确保相同物料相同单位的需求被合并
    const key = `${requirement.materialId}_${requirement.unit}`

    if (materialMap.has(key)) {
      // 如果已存在相同物料，则合并数量和金额
      const existing = materialMap.get(key)
      existing.totalQuantity = (parseFloat(existing.totalQuantity) || 0) + (parseFloat(requirement.quantity) || 0)
      existing.totalAmount = (parseFloat(existing.totalAmount) || 0) + (parseFloat(requirement.amount) || 0)
      existing.requirements.push(requirement)

      // 合并备注信息
      if (requirement.remark && requirement.remark.trim()) {
        if (existing.combinedRemark) {
          existing.combinedRemark += '; ' + requirement.remark
        } else {
          existing.combinedRemark = requirement.remark
        }
      }

      // 合并来源单号信息
      if (requirement.requestNo && requirement.requestNo.trim()) {
        if (existing.combinedSourceNo) {
          // 避免重复添加相同的来源单号
          if (!existing.combinedSourceNo.includes(requirement.requestNo)) {
            existing.combinedSourceNo += '; ' + requirement.requestNo
          }
        } else {
          existing.combinedSourceNo = requirement.requestNo
        }
      }
    } else {
      // 如果是新物料，创建新的合并记录
      materialMap.set(key, {
        materialId: requirement.materialId,
        materialName: requirement.materialName,
        materialCode: requirement.materialCode,
        unit: requirement.unit,
        unitPrice: requirement.unitPrice || 0,
        totalQuantity: parseFloat(requirement.quantity) || 0,
        totalAmount: parseFloat(requirement.amount) || 0,
        combinedRemark: requirement.remark || '',
        combinedSourceNo: requirement.requestNo || '', // 初始化来源单号
        requirements: [requirement] // 存储所有相关的需求单
      })
    }
  }

  return Array.from(materialMap.values())
}

defineExpose({ validate, getData, setRequirementData, setMultipleRequirementData })

/** 组件挂载时初始化单位数据 */
onMounted(async () => {
  await initUnitMap()
})
</script>

<style scoped>
.material-info-cell {
  padding: 4px 0;
  line-height: 1.4;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
  font-size: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #606266;
  font-weight: 500;
  min-width: 70px;
  flex-shrink: 0;
}

.info-value {
  color: #303133;
  flex: 1;
  word-break: break-all;
}
</style>
